//
//  SpatialViewModel.swift
//  FirmBond
//
//  Created by <PERSON><PERSON> on 02/06/2025.
//

import SwiftUI
import CoreData
import Combine

@MainActor
class SpatialViewModel: ObservableObject {
    
    // MARK: - Published Properties
    @Published var spaces: [MemorySpace] = []
    @Published var currentSpaceIndex: Int = 0
    @Published var showRelationships: Bool = true
    @Published var isLoading: Bool = false
    @Published var errorMessage: String?
    @Published var selectedContact: SpatialContact?
    
    // MARK: - Private Properties
    private let contactsViewModel: ContactsViewModel
    private let viewContext: NSManagedObjectContext
    private var cancellables = Set<AnyCancellable>()
    private let userDefaults = UserDefaults.standard
    
    // MARK: - Constants
    private let spacesKey = "SpatialSpaces"
    private let currentSpaceKey = "CurrentSpaceIndex"
    
    // MARK: - Computed Properties
    var currentSpace: MemorySpace? {
        guard currentSpaceIndex < spaces.count else { return nil }
        return spaces[currentSpaceIndex]
    }
    
    var allSpatialContacts: [SpatialContact] {
        return spaces.flatMap { $0.contacts }
    }
    
    var allRelationships: [ContactRelationship] {
        return spaces.flatMap { $0.relationships }
    }
    
    // MARK: - Initialization
    init(contactsViewModel: ContactsViewModel, viewContext: NSManagedObjectContext) {
        self.contactsViewModel = contactsViewModel
        self.viewContext = viewContext
        
        setupObservers()
        loadSpaces()
        
        // If no spaces exist, create defaults
        if spaces.isEmpty {
            createDefaultSpaces()
        }
    }
    
    // MARK: - Setup
    private func setupObservers() {
        // Listen for changes in contacts
        contactsViewModel.$people
            .sink { [weak self] people in
                self?.syncContactsWithSpaces(people)
            }
            .store(in: &cancellables)
    }
    
    // MARK: - Space Management
    func createDefaultSpaces() {
        spaces = DefaultSpacesFactory.createDefaultSpaces()
        saveSpaces()
        
        // Auto-place existing contacts
        autoPlaceExistingContacts()

        // Create sample relationships for demonstration
        createSampleRelationships()
    }
    
    func addSpace(_ space: MemorySpace) {
        spaces.append(space)
        saveSpaces()
    }
    
    func removeSpace(at index: Int) {
        guard index < spaces.count else { return }
        
        // Move contacts from removed space to default space
        let contactsToMove = spaces[index].contacts
        if !contactsToMove.isEmpty && spaces.count > 1 {
            let defaultSpaceIndex = 0
            for contact in contactsToMove {
                moveContact(contact, to: defaultSpaceIndex)
            }
        }
        
        spaces.remove(at: index)
        
        // Adjust current space index if necessary
        if currentSpaceIndex >= spaces.count {
            currentSpaceIndex = max(0, spaces.count - 1)
        }
        
        saveSpaces()
    }
    
    func switchToSpace(at index: Int) {
        guard index >= 0 && index < spaces.count else { return }
        currentSpaceIndex = index
        userDefaults.set(currentSpaceIndex, forKey: currentSpaceKey)
    }
    
    // MARK: - Contact Management
    func addContactToSpace(_ person: Person, spaceIndex: Int, position: CGPoint? = nil) {
        guard spaceIndex < spaces.count else { return }
        
        // Check if contact already exists in any space
        if let existingContact = findSpatialContact(for: person.id!) {
            // Move to new space instead of creating duplicate
            moveContact(existingContact, to: spaceIndex, position: position)
            return
        }
        
        let finalPosition = position ?? generateRandomPosition()
        let spatialContact = SpatialContact(
            personId: person.id!,
            position: finalPosition,
            spaceId: spaces[spaceIndex].id
        )
        
        spaces[spaceIndex].contacts.append(spatialContact)
        saveSpaces()
    }
    
    func moveContact(_ contact: SpatialContact, to spaceIndex: Int, position: CGPoint? = nil) {
        guard spaceIndex < spaces.count else { return }
        
        // Remove from current space
        for i in 0..<spaces.count {
            spaces[i].contacts.removeAll { $0.id == contact.id }
        }
        
        // Add to new space
        var updatedContact = contact
        updatedContact.spaceId = spaces[spaceIndex].id
        if let newPosition = position {
            updatedContact.position = newPosition
        }
        
        spaces[spaceIndex].contacts.append(updatedContact)
        saveSpaces()
    }
    
    func updateContactPosition(_ contactId: UUID, position: CGPoint) {
        for spaceIndex in 0..<spaces.count {
            if let contactIndex = spaces[spaceIndex].contacts.firstIndex(where: { $0.id == contactId }) {
                spaces[spaceIndex].contacts[contactIndex].position = position
                saveSpaces()
                return
            }
        }
    }
    
    func removeContact(_ contactId: UUID) {
        for spaceIndex in 0..<spaces.count {
            spaces[spaceIndex].contacts.removeAll { $0.id == contactId }
        }
        saveSpaces()
    }
    
    // MARK: - Relationship Management
    func addRelationship(_ relationship: ContactRelationship, to spaceIndex: Int) {
        guard spaceIndex < spaces.count else { return }

        // Check if relationship already exists
        let existingRelationship = spaces[spaceIndex].relationships.first { existing in
            (existing.fromContactId == relationship.fromContactId && existing.toContactId == relationship.toContactId) ||
            (existing.fromContactId == relationship.toContactId && existing.toContactId == relationship.fromContactId)
        }

        if existingRelationship == nil {
            spaces[spaceIndex].relationships.append(relationship)
            saveSpaces()
        }
    }
    
    func removeRelationship(_ relationshipId: UUID) {
        for spaceIndex in 0..<spaces.count {
            spaces[spaceIndex].relationships.removeAll { $0.id == relationshipId }
        }
        saveSpaces()
    }
    
    func updateRelationshipStrength(_ relationshipId: UUID, strength: Double) {
        for spaceIndex in 0..<spaces.count {
            if let relationshipIndex = spaces[spaceIndex].relationships.firstIndex(where: { $0.id == relationshipId }) {
                spaces[spaceIndex].relationships[relationshipIndex].strength = max(0.0, min(1.0, strength))
                spaces[spaceIndex].relationships[relationshipIndex].lastUpdated = Date()
                saveSpaces()
                return
            }
        }
    }
    
    // MARK: - Helper Methods
    private func findSpatialContact(for personId: UUID) -> SpatialContact? {
        for space in spaces {
            if let contact = space.contacts.first(where: { $0.personId == personId }) {
                return contact
            }
        }
        return nil
    }
    
    private func generateRandomPosition() -> CGPoint {
        // Generate position within safe bounds (avoiding edges)
        let margin: CGFloat = 80
        let screenWidth = max(200, UIScreen.main.bounds.width) // Ensure minimum width
        let screenHeight = max(400, UIScreen.main.bounds.height) // Ensure minimum height

        let maxX = screenWidth - margin
        let maxY = screenHeight - margin

        // Ensure valid range
        guard maxX > margin && maxY > margin else {
            return CGPoint(x: screenWidth / 2, y: screenHeight / 2)
        }

        let x = CGFloat.random(in: margin...maxX)
        let y = CGFloat.random(in: margin...maxY)

        return CGPoint(x: x, y: y)
    }
    
    private func autoPlaceExistingContacts() {
        let people = contactsViewModel.people
        
        for person in people {
            guard let personId = person.id else { continue }
            
            // Skip if already placed
            if findSpatialContact(for: personId) != nil { continue }
            
            // Determine best space based on relationship
            let spaceIndex = suggestSpaceForPerson(person)
            addContactToSpace(person, spaceIndex: spaceIndex)
        }
    }
    
    private func suggestSpaceForPerson(_ person: Person) -> Int {
        let relationship = person.relationship?.lowercased() ?? ""
        
        // Simple keyword-based placement
        if relationship.contains("family") || relationship.contains("mom") || 
           relationship.contains("dad") || relationship.contains("sister") || 
           relationship.contains("brother") {
            return spaces.firstIndex { $0.spaceType == .family } ?? 0
        }
        
        if relationship.contains("work") || relationship.contains("colleague") || 
           relationship.contains("boss") || relationship.contains("client") {
            return spaces.firstIndex { $0.spaceType == .work } ?? 0
        }
        
        if relationship.contains("friend") {
            return spaces.firstIndex { $0.spaceType == .social } ?? 0
        }
        
        // Default to home space
        return 0
    }
    
    private func syncContactsWithSpaces(_ people: [Person]) {
        // Remove spatial contacts for deleted people
        let peopleIds = Set(people.compactMap { $0.id })
        
        for spaceIndex in 0..<spaces.count {
            spaces[spaceIndex].contacts.removeAll { contact in
                !peopleIds.contains(contact.personId)
            }
        }
        
        saveSpaces()
    }

    private func createSampleRelationships() {
        // Only create sample relationships if we have contacts and no existing relationships
        guard !spaces.isEmpty else { return }

        for spaceIndex in 0..<spaces.count {
            let contacts = spaces[spaceIndex].contacts
            guard contacts.count >= 2 && spaces[spaceIndex].relationships.isEmpty else { continue }

            // Create a sample relationship between the first two contacts
            let relationship = ContactRelationship(
                fromContactId: contacts[0].id,
                toContactId: contacts[1].id,
                type: .friend,
                strength: 0.7
            )

            spaces[spaceIndex].relationships.append(relationship)
        }

        saveSpaces()
    }

    // MARK: - Persistence
    private func saveSpaces() {
        do {
            let data = try JSONEncoder().encode(spaces)
            userDefaults.set(data, forKey: spacesKey)
        } catch {
            errorMessage = "Failed to save spaces: \(error.localizedDescription)"
        }
    }
    
    private func loadSpaces() {
        guard let data = userDefaults.data(forKey: spacesKey) else { return }
        
        do {
            spaces = try JSONDecoder().decode([MemorySpace].self, from: data)
            currentSpaceIndex = userDefaults.integer(forKey: currentSpaceKey)
            
            // Ensure current space index is valid
            if currentSpaceIndex >= spaces.count {
                currentSpaceIndex = 0
            }
        } catch {
            errorMessage = "Failed to load spaces: \(error.localizedDescription)"
            // Create defaults if loading fails
            createDefaultSpaces()
        }
    }

    func getRelationshipsForContact(_ contactId: UUID) -> [ContactRelationship] {
        guard currentSpaceIndex < spaces.count else { return [] }

        return spaces[currentSpaceIndex].relationships.filter { relationship in
            relationship.fromContactId == contactId || relationship.toContactId == contactId
        }
    }

    // MARK: - Public Helpers
    func getPerson(for spatialContact: SpatialContact) -> Person? {
        return contactsViewModel.people.first { $0.id == spatialContact.personId }
    }

    func getContactsInCurrentSpace() -> [SpatialContact] {
        return currentSpace?.contacts ?? []
    }

    func getRelationshipsInCurrentSpace() -> [ContactRelationship] {
        return currentSpace?.relationships ?? []
    }
}
