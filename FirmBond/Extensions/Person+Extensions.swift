//
//  Person+Extensions.swift
//  FirmBond
//
//  Created by <PERSON><PERSON> on 02/06/2025.
//

import Foundation
import CoreData
import SwiftUI

extension Person {

    /// Display name for relationship context
    var displayName: String {
        if let relationship = relationship, !relationship.trimmingCharacters(in: .whitespaces).isEmpty {
            return "\(fullName) (\(relationship))"
        }
        return fullName
    }
    
    /// Primary contact method
    var primaryContactMethod: ContactMethod? {
        if !(phoneNumber?.trimmingCharacters(in: .whitespaces).isEmpty ?? true) {
            return .phone
        } else if !(email?.trimmingCharacters(in: .whitespaces).isEmpty ?? true) {
            return .email
        }
        return nil
    }
    
    /// Days since last contact
    var daysSinceLastContact: Int? {
        guard let lastContact = lastContactDate else { return nil }
        return Calendar.current.dateComponents([.day], from: lastContact, to: Date()).day
    }
    
    /// Contact frequency category
    var contactFrequency: ContactFrequency {
        guard let days = daysSinceLastContact else { return .never }
        
        switch days {
        case 0...3:
            return .frequent
        case 4...14:
            return .regular
        case 15...30:
            return .occasional
        case 31...90:
            return .rare
        default:
            return .distant
        }
    }
    
    /// Suggested space type based on relationship
    var suggestedSpaceType: SpaceType {
        let relationshipLower = relationship?.lowercased() ?? ""
        
        if relationshipLower.contains("family") || 
           relationshipLower.contains("mom") || 
           relationshipLower.contains("dad") || 
           relationshipLower.contains("sister") || 
           relationshipLower.contains("brother") ||
           relationshipLower.contains("parent") ||
           relationshipLower.contains("sibling") {
            return .family
        }
        
        if relationshipLower.contains("work") || 
           relationshipLower.contains("colleague") || 
           relationshipLower.contains("boss") || 
           relationshipLower.contains("client") ||
           relationshipLower.contains("coworker") ||
           relationshipLower.contains("manager") {
            return .work
        }
        
        if relationshipLower.contains("friend") {
            return .social
        }
        
        if relationshipLower.contains("neighbor") {
            return .home
        }
        
        // Default to social for general relationships
        return .social
    }
}

// MARK: - Supporting Enums

enum ContactMethod {
    case phone
    case email
    case message
    
    var icon: String {
        switch self {
        case .phone: return "phone.fill"
        case .email: return "envelope.fill"
        case .message: return "message.fill"
        }
    }
    
    var displayName: String {
        switch self {
        case .phone: return "Call"
        case .email: return "Email"
        case .message: return "Message"
        }
    }
}

enum ContactFrequency: CaseIterable {
    case frequent   // 0-3 days
    case regular    // 4-14 days
    case occasional // 15-30 days
    case rare       // 31-90 days
    case distant    // 90+ days
    case never      // No contact recorded
    
    var displayName: String {
        switch self {
        case .frequent: return "Frequent"
        case .regular: return "Regular"
        case .occasional: return "Occasional"
        case .rare: return "Rare"
        case .distant: return "Distant"
        case .never: return "Never"
        }
    }
    
    var color: Color {
        switch self {
        case .frequent: return .green
        case .regular: return .blue
        case .occasional: return .orange
        case .rare: return .red
        case .distant: return .gray
        case .never: return .black
        }
    }
    
    var description: String {
        switch self {
        case .frequent: return "Within 3 days"
        case .regular: return "Within 2 weeks"
        case .occasional: return "Within a month"
        case .rare: return "Within 3 months"
        case .distant: return "Over 3 months ago"
        case .never: return "No contact recorded"
        }
    }
}

// MARK: - Spatial Extensions

extension Person {
    
    /// Generate a suggested position for spatial placement
    func suggestedPosition(in bounds: CGRect, avoiding existingPositions: [CGPoint] = []) -> CGPoint {
        let margin: CGFloat = 60
        let safeWidth = bounds.width - (margin * 2)
        let safeHeight = bounds.height - (margin * 2)
        
        var attempts = 0
        let maxAttempts = 20
        
        while attempts < maxAttempts {
            let x = CGFloat.random(in: margin...(margin + safeWidth))
            let y = CGFloat.random(in: margin...(margin + safeHeight))
            let proposedPosition = CGPoint(x: x, y: y)
            
            // Check if position conflicts with existing contacts
            let minDistance: CGFloat = 80
            let hasConflict = existingPositions.contains { existingPosition in
                let distance = sqrt(
                    pow(proposedPosition.x - existingPosition.x, 2) +
                    pow(proposedPosition.y - existingPosition.y, 2)
                )
                return distance < minDistance
            }
            
            if !hasConflict {
                return proposedPosition
            }
            
            attempts += 1
        }
        
        // Fallback to random position if no good spot found
        return CGPoint(
            x: CGFloat.random(in: margin...(margin + safeWidth)),
            y: CGFloat.random(in: margin...(margin + safeHeight))
        )
    }
}

import SwiftUI
