//
//  ContactNodeView.swift
//  FirmBond
//
//  Created by <PERSON><PERSON> on 02/06/2025.
//

import SwiftUI

struct ContactNodeView: View {
    let contact: SpatialContact
    @ObservedObject var spatialViewModel: SpatialViewModel
    @ObservedObject var contactsViewModel: ContactsViewModel
    let isSelected: Bool
    let onTap: () -> Void
    let onDragChanged: (DragGesture.Value) -> Void
    let onDragEnded: (DragGesture.Value) -> Void
    
    @State private var isPressed = false
    @State private var scale: CGFloat = 1.0
    
    var body: some View {
        Group {
            if let person = spatialViewModel.getPerson(for: contact) {
                ContactBubbleContent(person: person, isSelected: isSelected)
            } else {
                // Fallback for missing person data
                Circle()
                    .fill(DesignSystem.Colors.lightGray)
                    .frame(width: 60, height: 60)
                    .overlay(
                        Text("?")
                            .font(.title2)
                            .foregroundColor(.white)
                    )
            }
        }
        .scaleEffect(scale)
        .scaleEffect(isPressed ? 0.95 : 1.0)
        .position(contact.position)
        .gesture(
            DragGesture()
                .onChanged { value in
                    onDragChanged(value)
                }
                .onEnded { value in
                    onDragEnded(value)
                }
        )
        .onTapGesture {
            onTap()
            
            // Haptic feedback
            let impactFeedback = UIImpactFeedbackGenerator(style: .medium)
            impactFeedback.impactOccurred()
        }
        .onLongPressGesture(minimumDuration: 0) { pressing in
            withAnimation(.easeInOut(duration: 0.1)) {
                isPressed = pressing
            }
        } perform: {}
        .animation(.spring(response: 0.3, dampingFraction: 0.7), value: isSelected)
    }
}

// MARK: - Contact Bubble Content
struct ContactBubbleContent: View {
    let person: Person
    let isSelected: Bool
    
    var body: some View {
        ZStack {
            // Background circle
            Circle()
                .fill(.ultraThinMaterial)
                .overlay(
                    Circle()
                        .stroke(
                            isSelected ? DesignSystem.Colors.mutedGold : DesignSystem.Colors.glassBorder,
                            lineWidth: isSelected ? 3 : 1
                        )
                )
                .frame(width: 60, height: 60)
            
            // Profile image or initials
            if let photoData = person.photoData,
               let uiImage = UIImage(data: photoData) {
                Image(uiImage: uiImage)
                    .resizable()
                    .aspectRatio(contentMode: .fill)
                    .frame(width: 54, height: 54)
                    .clipShape(Circle())
            } else {
                Text(person.initials)
                    .font(.system(size: 18, weight: .semibold))
                    .foregroundColor(DesignSystem.Colors.warmBlack)
            }
        }
        .shadow(
            color: DesignSystem.Colors.glassShadow,
            radius: isSelected ? 8 : 4,
            x: 0,
            y: isSelected ? 4 : 2
        )
    }
}

// MARK: - Preview
#Preview {
    ContactNodeView(
        contact: SpatialContact(
            personId: UUID(),
            position: CGPoint(x: 100, y: 100),
            spaceId: UUID()
        ),
        spatialViewModel: SpatialViewModel(
            contactsViewModel: ContactsViewModel(viewContext: PersistenceController.preview.container.viewContext),
            viewContext: PersistenceController.preview.container.viewContext
        ),
        contactsViewModel: ContactsViewModel(viewContext: PersistenceController.preview.container.viewContext),
        isSelected: false,
        onTap: {},
        onDragChanged: { _ in },
        onDragEnded: { _ in }
    )
}
