//
//  MemorySpaceView.swift
//  FirmBond
//
//  Created by <PERSON><PERSON> on 02/06/2025.
//

import SwiftUI

struct MemorySpaceView: View {
    
    // MARK: - Properties
    let space: MemorySpace
    @ObservedObject var spatialViewModel: SpatialViewModel
    @ObservedObject var contactsViewModel: ContactsViewModel
    let isActive: Bool
    let geometry: GeometryProxy
    
    // MARK: - State
    @State private var selectedContact: SpatialContact?
    @State private var draggedContact: SpatialContact?
    @State private var dragOffset: CGSize = .zero
    @State private var showingContactDetail: Bool = false
    
    // MARK: - Body
    var body: some View {
        ZStack {
            // Background
            spaceBackground
            
            // Contacts
            contactsLayer
            
            // Space title overlay
            if isActive {
                spaceTitleOverlay
            }
        }
        .clipped()
        .sheet(item: $selectedContact) { contact in
            if let person = spatialViewModel.getPerson(for: contact) {
                UnifiedContactView(mode: .view(person), viewModel: contactsViewModel)
            }
        }
    }
    
    // MARK: - Background
    private var spaceBackground: some View {
        ZStack {
            // Base gradient
            LinearGradient(
                colors: [
                    space.color.opacity(0.1),
                    space.color.opacity(0.05),
                    Color.clear
                ],
                startPoint: .topLeading,
                endPoint: .bottomTrailing
            )
            
            // Space-specific background elements
            backgroundElements
        }
    }
    
    private var backgroundElements: some View {
        Group {
            switch space.spaceType {
            case .home:
                homeBackground
            case .work:
                workBackground
            case .social:
                socialBackground
            case .family:
                familyBackground
            case .places:
                placesBackground
            case .hobbies:
                hobbiesBackground
            }
        }
        .opacity(0.3)
    }
    
    // MARK: - Space-Specific Backgrounds
    private var homeBackground: some View {
        VStack {
            Spacer()
            HStack {
                // Couch silhouette
                RoundedRectangle(cornerRadius: 8)
                    .fill(DesignSystem.Colors.mutedGold.opacity(0.2))
                    .frame(width: 100, height: 30)
                
                Spacer()
                
                // Coffee table
                RoundedRectangle(cornerRadius: 4)
                    .fill(DesignSystem.Colors.mutedGold.opacity(0.15))
                    .frame(width: 50, height: 15)
                
                Spacer()
            }
            .padding(.bottom, 80)
        }
    }
    
    private var workBackground: some View {
        VStack {
            Spacer()
            HStack {
                // Desk
                Rectangle()
                    .fill(DesignSystem.Colors.sageGreen.opacity(0.2))
                    .frame(width: 120, height: 8)
                
                Spacer()
                
                // Chair
                RoundedRectangle(cornerRadius: 4)
                    .fill(DesignSystem.Colors.sageGreen.opacity(0.15))
                    .frame(width: 40, height: 25)
            }
            .padding(.bottom, 80)
        }
    }
    
    private var socialBackground: some View {
        // Scattered circles representing social gatherings
        ForEach(0..<5, id: \.self) { index in
            Circle()
                .fill(DesignSystem.Colors.softBlue.opacity(0.1))
                .frame(width: CGFloat.random(in: 20...40))
                .position(
                    x: safeRandomX(),
                    y: safeRandomY()
                )
        }
    }
    
    private var familyBackground: some View {
        // Heart shapes scattered around
        ForEach(0..<3, id: \.self) { index in
            Image(systemName: "heart.fill")
                .font(.system(size: CGFloat.random(in: 20...35)))
                .foregroundColor(.red.opacity(0.1))
                .position(
                    x: safeRandomX(),
                    y: safeRandomY()
                )
        }
    }
    
    private var placesBackground: some View {
        // Location pins
        ForEach(0..<4, id: \.self) { index in
            Image(systemName: "location.fill")
                .font(.system(size: CGFloat.random(in: 15...25)))
                .foregroundColor(.purple.opacity(0.15))
                .position(
                    x: safeRandomX(),
                    y: safeRandomY()
                )
        }
    }
    
    private var hobbiesBackground: some View {
        // Game controller and hobby icons
        ForEach(0..<3, id: \.self) { index in
            Image(systemName: ["gamecontroller.fill", "paintbrush.fill", "music.note"].randomElement() ?? "gamecontroller.fill")
                .font(.system(size: CGFloat.random(in: 20...30)))
                .foregroundColor(.orange.opacity(0.1))
                .position(
                    x: safeRandomX(),
                    y: safeRandomY()
                )
        }
    }
    
    // MARK: - Contacts Layer
    private var contactsLayer: some View {
        ForEach(space.contacts, id: \.id) { contact in
            ContactNodeView(
                contact: contact,
                spatialViewModel: spatialViewModel,
                contactsViewModel: contactsViewModel,
                isSelected: selectedContact?.id == contact.id,
                onTap: {
                    selectedContact = contact
                },
                onDragChanged: { value in
                    handleContactDragChanged(contact, value: value)
                },
                onDragEnded: { value in
                    handleContactDragEnded(contact, value: value)
                }
            )
        }
    }
    
    // MARK: - Space Title Overlay
    private var spaceTitleOverlay: some View {
        VStack {
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    HStack(spacing: 8) {
                        Image(systemName: space.icon)
                            .font(.title2)
                            .foregroundColor(space.color)
                        
                        Text(space.name)
                            .font(DesignSystem.Typography.title2)
                            .foregroundColor(DesignSystem.Colors.warmBlack)
                    }
                    
                    Text("\(space.contacts.count) contacts")
                        .font(DesignSystem.Typography.caption)
                        .foregroundColor(DesignSystem.Colors.subtleText)
                }
                
                Spacer()
            }
            .padding(.horizontal, 20)
            .padding(.top, 10)
            
            Spacer()
        }
    }
    
    // MARK: - Gesture Handling
    private func handleContactDragChanged(_ contact: SpatialContact, value: DragGesture.Value) {
        draggedContact = contact
        dragOffset = value.translation
        
        // Update contact position in real-time
        let newPosition = CGPoint(
            x: contact.position.x + value.translation.width,
            y: contact.position.y + value.translation.height
        )
        
        spatialViewModel.updateContactPosition(contact.id, position: newPosition)
    }
    
    private func handleContactDragEnded(_ contact: SpatialContact, value: DragGesture.Value) {
        let finalPosition = CGPoint(
            x: contact.position.x + value.translation.width,
            y: contact.position.y + value.translation.height
        )
        
        // Ensure position is within bounds
        let boundedPosition = boundPosition(finalPosition)
        spatialViewModel.updateContactPosition(contact.id, position: boundedPosition)
        
        // Reset drag state
        draggedContact = nil
        dragOffset = .zero
        
        // Haptic feedback
        let impactFeedback = UIImpactFeedbackGenerator(style: .light)
        impactFeedback.impactOccurred()
    }
    
    private func boundPosition(_ position: CGPoint) -> CGPoint {
        let margin: CGFloat = 40
        let maxX = geometry.size.width - margin
        let maxY = geometry.size.height - margin
        
        return CGPoint(
            x: max(margin, min(maxX, position.x)),
            y: max(margin, min(maxY, position.y))
        )
    }

    // MARK: - Safe Random Position Helpers
    private func safeRandomX() -> CGFloat {
        let margin: CGFloat = 50
        let width = max(100, geometry.size.width) // Ensure minimum width
        let maxX = width - margin

        guard maxX > margin else { return width / 2 }
        return CGFloat.random(in: margin...maxX)
    }

    private func safeRandomY() -> CGFloat {
        let margin: CGFloat = 100
        let height = max(200, geometry.size.height) // Ensure minimum height
        let maxY = height - margin

        guard maxY > margin else { return height / 2 }
        return CGFloat.random(in: margin...maxY)
    }
}

// MARK: - Contact Node View
struct ContactNodeView: View {
    let contact: SpatialContact
    @ObservedObject var spatialViewModel: SpatialViewModel
    @ObservedObject var contactsViewModel: ContactsViewModel
    let isSelected: Bool
    let onTap: () -> Void
    let onDragChanged: (DragGesture.Value) -> Void
    let onDragEnded: (DragGesture.Value) -> Void
    
    @State private var isPressed = false
    @State private var scale: CGFloat = 1.0
    
    var body: some View {
        Group {
            if let person = spatialViewModel.getPerson(for: contact) {
                ContactBubbleContent(person: person, isSelected: isSelected)
            } else {
                // Fallback for missing person data
                Circle()
                    .fill(DesignSystem.Colors.lightGray)
                    .frame(width: 60, height: 60)
                    .overlay(
                        Text("?")
                            .font(.title2)
                            .foregroundColor(.white)
                    )
            }
        }
        .scaleEffect(scale)
        .scaleEffect(isPressed ? 0.95 : 1.0)
        .position(contact.position)
        .gesture(
            DragGesture()
                .onChanged { value in
                    onDragChanged(value)
                }
                .onEnded { value in
                    onDragEnded(value)
                }
        )
        .onTapGesture {
            onTap()
            
            // Haptic feedback
            let impactFeedback = UIImpactFeedbackGenerator(style: .medium)
            impactFeedback.impactOccurred()
        }
        .onLongPressGesture(minimumDuration: 0) { pressing in
            withAnimation(.easeInOut(duration: 0.1)) {
                isPressed = pressing
            }
        } perform: {}
        .animation(.spring(response: 0.3, dampingFraction: 0.7), value: isSelected)
    }
}

// MARK: - Contact Bubble Content
struct ContactBubbleContent: View {
    let person: Person
    let isSelected: Bool
    
    var body: some View {
        ZStack {
            // Background circle
            Circle()
                .fill(.ultraThinMaterial)
                .overlay(
                    Circle()
                        .stroke(
                            isSelected ? DesignSystem.Colors.mutedGold : DesignSystem.Colors.glassBorder,
                            lineWidth: isSelected ? 3 : 1
                        )
                )
                .frame(width: 60, height: 60)
            
            // Profile image or initials
            if let photoData = person.photoData,
               let uiImage = UIImage(data: photoData) {
                Image(uiImage: uiImage)
                    .resizable()
                    .aspectRatio(contentMode: .fill)
                    .frame(width: 54, height: 54)
                    .clipShape(Circle())
            } else {
                Text(person.initials)
                    .font(.system(size: 18, weight: .semibold))
                    .foregroundColor(DesignSystem.Colors.warmBlack)
            }
        }
        .shadow(
            color: DesignSystem.Colors.glassShadow,
            radius: isSelected ? 8 : 4,
            x: 0,
            y: isSelected ? 4 : 2
        )
    }
}

// MARK: - Preview
#Preview {
    GeometryReader { geometry in
        MemorySpaceView(
            space: DefaultSpacesFactory.createDefaultSpaces()[0],
            spatialViewModel: SpatialViewModel(
                contactsViewModel: ContactsViewModel(viewContext: PersistenceController.preview.container.viewContext),
                viewContext: PersistenceController.preview.container.viewContext
            ),
            contactsViewModel: ContactsViewModel(viewContext: PersistenceController.preview.container.viewContext),
            isActive: true,
            geometry: geometry
        )
    }
}
