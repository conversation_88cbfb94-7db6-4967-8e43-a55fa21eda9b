//
//  SpatialNavigationView.swift
//  FirmBond
//
//  Created by <PERSON><PERSON> on 02/06/2025.
//

import SwiftUI
import CoreData

struct SpatialNavigationView: View {
    
    // MARK: - Properties
    @StateObject private var spatialViewModel: SpatialViewModel
    @ObservedObject private var contactsViewModel: ContactsViewModel
    @Environment(\.managedObjectContext) private var viewContext
    
    // MARK: - State
    @State private var dragOffset: CGFloat = 0
    @State private var isDragging: Bool = false
    @State private var showingSpaceSelector: Bool = false
    @State private var showingRelationshipCreation: Bool = false
    @State private var hapticFeedback = UIImpactFeedbackGenerator(style: .medium)
    
    // MARK: - Constants
    private let spaceTransitionThreshold: CGFloat = 100
    
    // MARK: - Initialization
    init(contactsViewModel: ContactsViewModel, viewContext: NSManagedObjectContext) {
        self.contactsViewModel = contactsViewModel
        self._spatialViewModel = StateObject(wrappedValue: SpatialViewModel(
            contactsViewModel: contactsViewModel,
            viewContext: viewContext
        ))
    }
    
    // MARK: - Body
    var body: some View {
        NavigationStack {
            GeometryReader { geometry in
                ZStack {
                    // Background gradient
                    backgroundGradient
                    
                    // Main spatial content
                    spatialContent(geometry: geometry)
                    
                    // Space indicator overlay
                    spaceIndicatorOverlay
                    
                    // Floating controls
                    floatingControls
                }
                .ignoresSafeArea(edges: .bottom)
            }
            .navigationTitle("Memory Palace")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button(action: { showingSpaceSelector = true }) {
                        HStack(spacing: 4) {
                            Image(systemName: spatialViewModel.currentSpace?.icon ?? "house.fill")
                            Text(spatialViewModel.currentSpace?.name ?? "Home")
                                .font(.caption)
                        }
                        .foregroundColor(DesignSystem.Colors.mutedGold)
                    }
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button(action: { spatialViewModel.showRelationships.toggle() }) {
                        Image(systemName: spatialViewModel.showRelationships ? "link" : "link.badge.plus")
                            .foregroundColor(DesignSystem.Colors.mutedGold)
                    }
                }
            }
        }
        .sheet(isPresented: $showingSpaceSelector) {
            SpaceSelectorView(spatialViewModel: spatialViewModel)
        }
        .sheet(isPresented: $showingRelationshipCreation) {
            RelationshipCreationView(spatialViewModel: spatialViewModel)
        }
        .onAppear {
            hapticFeedback.prepare()
        }
    }
    
    // MARK: - Background
    private var backgroundGradient: some View {
        LinearGradient(
            colors: [
                spatialViewModel.currentSpace?.color.opacity(0.1) ?? DesignSystem.Colors.primaryCream,
                DesignSystem.Colors.secondaryCream,
                DesignSystem.Colors.warmGray.opacity(0.2)
            ],
            startPoint: .topLeading,
            endPoint: .bottomTrailing
        )
        .ignoresSafeArea()
    }
    
    // MARK: - Spatial Content
    private func spatialContent(geometry: GeometryProxy) -> some View {
        HStack(spacing: 0) {
            ForEach(Array(spatialViewModel.spaces.enumerated()), id: \.element.id) { index, space in
                MemorySpaceView(
                    space: space,
                    spatialViewModel: spatialViewModel,
                    contactsViewModel: contactsViewModel,
                    isActive: index == spatialViewModel.currentSpaceIndex,
                    geometry: geometry
                )
                .frame(width: geometry.size.width)
            }
        }
        .offset(x: calculateContentOffset(geometry: geometry))
        .gesture(
            DragGesture()
                .onChanged { value in
                    handleDragChanged(value)
                }
                .onEnded { value in
                    handleDragEnded(value, geometry: geometry)
                }
        )
        .animation(.spring(response: 0.6, dampingFraction: 0.8), value: spatialViewModel.currentSpaceIndex)
        .animation(.spring(response: 0.3, dampingFraction: 0.9), value: dragOffset)
    }
    
    // MARK: - Space Indicator
    private var spaceIndicatorOverlay: some View {
        VStack {
            Spacer()
            
            HStack(spacing: 8) {
                ForEach(Array(spatialViewModel.spaces.enumerated()), id: \.element.id) { index, space in
                    Circle()
                        .fill(index == spatialViewModel.currentSpaceIndex ? 
                              space.color : Color.gray.opacity(0.3))
                        .frame(width: 8, height: 8)
                        .scaleEffect(index == spatialViewModel.currentSpaceIndex ? 1.2 : 1.0)
                        .animation(.spring(response: 0.3, dampingFraction: 0.7), value: spatialViewModel.currentSpaceIndex)
                }
            }
            .padding(.horizontal, 16)
            .padding(.vertical, 8)
            .background(.ultraThinMaterial, in: Capsule())
            .padding(.bottom, 100) // Space for floating controls
        }
    }
    
    // MARK: - Floating Controls
    private var floatingControls: some View {
        VStack {
            Spacer()
            
            HStack {
                Spacer()
                
                VStack(spacing: 16) {
                    // Add contact button
                    FloatingActionButton(
                        icon: "person.badge.plus",
                        color: DesignSystem.Colors.mutedGold
                    ) {
                        addNewContact()
                    }

                    // Add relationship button
                    FloatingActionButton(
                        icon: spatialViewModel.showRelationships ? "link" : "link.badge.plus",
                        color: spatialViewModel.showRelationships ? DesignSystem.Colors.softBlue : DesignSystem.Colors.softBlue.opacity(0.6)
                    ) {
                        showingRelationshipCreation = true
                    }

                    // Space settings button
                    FloatingActionButton(
                        icon: "slider.horizontal.3",
                        color: DesignSystem.Colors.sageGreen
                    ) {
                        showingSpaceSelector = true
                    }
                }
                .padding(.trailing, 20)
                .padding(.bottom, 40)
            }
        }
    }
    
    // MARK: - Gesture Handling
    private func calculateContentOffset(geometry: GeometryProxy) -> CGFloat {
        return -CGFloat(spatialViewModel.currentSpaceIndex) * geometry.size.width + dragOffset
    }
    
    private func handleDragChanged(_ value: DragGesture.Value) {
        dragOffset = value.translation.width
        isDragging = true

        // Provide haptic feedback when approaching threshold
        let threshold = spaceTransitionThreshold
        if abs(dragOffset) > threshold && !isDragging {
            hapticFeedback.impactOccurred()
        }
    }
    
    private func handleDragEnded(_ value: DragGesture.Value, geometry: GeometryProxy) {
        let threshold = spaceTransitionThreshold
        let velocity = value.predictedEndTranslation.width
        
        var newIndex = spatialViewModel.currentSpaceIndex
        
        // Determine if we should switch spaces
        if dragOffset > threshold || velocity > 500 {
            // Swipe right - go to previous space
            newIndex = max(0, spatialViewModel.currentSpaceIndex - 1)
        } else if dragOffset < -threshold || velocity < -500 {
            // Swipe left - go to next space
            newIndex = min(spatialViewModel.spaces.count - 1, spatialViewModel.currentSpaceIndex + 1)
        }
        
        // Apply the change
        if newIndex != spatialViewModel.currentSpaceIndex {
            spatialViewModel.switchToSpace(at: newIndex)
            hapticFeedback.impactOccurred()
        }
        
        // Reset drag state
        dragOffset = 0
        isDragging = false
    }
    
    // MARK: - Actions
    private func addNewContact() {
        // For now, we'll integrate with the existing add contact flow
        // In Phase 2, we'll add spatial-specific contact creation
        contactsViewModel.isShowingAddContact = true
    }

    private func toggleRelationships() {
        withAnimation(.spring(response: 0.4, dampingFraction: 0.8)) {
            spatialViewModel.showRelationships.toggle()
        }

        // Haptic feedback
        let impactFeedback = UIImpactFeedbackGenerator(style: .medium)
        impactFeedback.impactOccurred()
    }
}

// MARK: - Space Selector Sheet
struct SpaceSelectorView: View {
    @ObservedObject var spatialViewModel: SpatialViewModel
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        NavigationStack {
            VStack(spacing: 20) {
                Text("Memory Spaces")
                    .font(DesignSystem.Typography.largeTitle)
                    .foregroundColor(DesignSystem.Colors.warmBlack)
                    .padding(.top)
                
                LazyVGrid(columns: [
                    GridItem(.flexible()),
                    GridItem(.flexible())
                ], spacing: 16) {
                    ForEach(Array(spatialViewModel.spaces.enumerated()), id: \.element.id) { index, space in
                        SpaceCard(
                            space: space,
                            isSelected: index == spatialViewModel.currentSpaceIndex,
                            contactCount: space.contacts.count
                        ) {
                            spatialViewModel.switchToSpace(at: index)
                            dismiss()
                        }
                    }
                }
                .padding(.horizontal)
                
                Spacer()
            }
            .background(DesignSystem.Colors.primaryCream)
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") {
                        dismiss()
                    }
                    .foregroundColor(DesignSystem.Colors.mutedGold)
                }
            }
        }
    }
}

// MARK: - Space Card Component
struct SpaceCard: View {
    let space: MemorySpace
    let isSelected: Bool
    let contactCount: Int
    let onTap: () -> Void
    
    @State private var isPressed = false
    
    var body: some View {
        Button(action: onTap) {
            VStack(spacing: 12) {
                // Icon
                Image(systemName: space.icon)
                    .font(.system(size: 32))
                    .foregroundColor(space.color)
                
                // Name
                Text(space.name)
                    .font(DesignSystem.Typography.headline)
                    .foregroundColor(DesignSystem.Colors.warmBlack)
                
                // Contact count
                Text("\(contactCount) contacts")
                    .font(DesignSystem.Typography.caption)
                    .foregroundColor(DesignSystem.Colors.subtleText)
            }
            .frame(height: 120)
            .frame(maxWidth: .infinity)
            .background(
                RoundedRectangle(cornerRadius: DesignSystem.CornerRadius.large)
                    .fill(.ultraThinMaterial)
                    .overlay(
                        RoundedRectangle(cornerRadius: DesignSystem.CornerRadius.large)
                            .stroke(isSelected ? space.color : Color.clear, lineWidth: 2)
                    )
            )
        }
        .buttonStyle(PlainButtonStyle())
        .scaleEffect(isPressed ? 0.95 : 1.0)
        .onLongPressGesture(minimumDuration: 0) { pressing in
            withAnimation(.easeInOut(duration: 0.1)) {
                isPressed = pressing
            }
        } perform: {}
    }
}

// MARK: - Preview
#Preview {
    SpatialNavigationView(
        contactsViewModel: ContactsViewModel(viewContext: PersistenceController.preview.container.viewContext),
        viewContext: PersistenceController.preview.container.viewContext
    )
}
